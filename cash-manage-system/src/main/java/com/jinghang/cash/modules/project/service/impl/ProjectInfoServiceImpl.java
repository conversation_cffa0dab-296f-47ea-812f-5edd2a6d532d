package com.jinghang.cash.modules.project.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghang.cash.api.dto.ProjectElementsExtDto;
import com.jinghang.cash.api.dto.ProjectElementsDto;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.cash.api.enums.*;
import com.jinghang.cash.modules.project.mapper.ProjectElementsExtMapper;
import com.jinghang.cash.modules.project.mapper.ProjectElementsMapper;
import com.jinghang.cash.modules.project.mapper.ProjectInfoMapper;
import com.jinghang.cash.modules.project.domain.ProjectElements;
import com.jinghang.cash.modules.project.domain.ProjectElementsExt;
import com.jinghang.cash.modules.project.domain.ProjectInfo;
import com.jinghang.cash.modules.project.domain.dto.ProjectInfoQueryCriteria;
import com.jinghang.cash.modules.project.service.ProjectInfoService;
import com.jinghang.cash.modules.project.service.domain.vo.ProjectElementsExtVo;
import com.jinghang.cash.modules.project.service.domain.vo.ProjectElementsVo;
import com.jinghang.cash.modules.project.service.domain.vo.ProjectInfoVo;
import com.jinghang.cash.utils.PageResult;
import com.jinghang.cash.utils.PageUtil;
import com.jinghang.cash.utils.RedisUtils;
import com.jinghang.cash.utils.SecurityUtils;
import com.jinghang.cash.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 项目信息服务实现类
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 10:16
 */
@Service
@RequiredArgsConstructor
public class ProjectInfoServiceImpl implements ProjectInfoService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectInfoServiceImpl.class);

    @Autowired
    private ProjectInfoMapper projectInfoMapper;

    @Autowired
    private ProjectElementsMapper projectElementsMapper;

    @Autowired
    private ProjectElementsExtMapper projectElementsExtMapper;


    private final RedisUtils redisUtils;

    /**
     * 项目信息缓存key前缀
     */
    private static final String PROJECT_INFO_CACHE_PREFIX = "cash_manage:project:info:";

    /**
     * 默认缓存过期时间：24 * 7小时
     */
    private static final Duration CACHE_DURATION = Duration.ofHours(24 * 7);

    /**
     * 根据项目编码查询项目完整信息（带缓存）
     * todo 数据日志
     *
     * @param projectCode 项目编码
     * @return 项目完整信息dto
     */
    @Override
    public ProjectInfoDto queryProjectInfo(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            logger.info("项目编码为空，无法查询项目信息");
            return null;
        }

        // 构建缓存key
        String cacheKey = PROJECT_INFO_CACHE_PREFIX + projectCode;

        try {
            // 1. 先从缓存中获取
            Object cachedData = redisUtils.get(cacheKey);
            if (cachedData instanceof ProjectInfoDto) {
                return (ProjectInfoDto) cachedData;
            }

            // 2. 缓存中没有，从数据库查询
            ProjectInfoDto projectInfoDto = queryProjectInfoFromDatabase(projectCode);

            // 3. 查询结果放入缓存
            if (projectInfoDto != null) {
                Duration cacheDuration = calculateCacheDuration(projectInfoDto.getElements());
                redisUtils.set(cacheKey, projectInfoDto, cacheDuration.getSeconds(), TimeUnit.SECONDS);
            }

            return projectInfoDto;

        } catch (Exception e) {
            logger.info("查询项目完整信息异常，projectCode: {}", projectCode, e);
            throw new RuntimeException("查询项目信息失败", e);
        }
    }

    /**
     * 从数据库查询项目完整信息
     *
     * @param projectCode 项目编码
     * @return 项目完整信息dto
     */
    private ProjectInfoDto queryProjectInfoFromDatabase(String projectCode) {
        // 1. 查询项目基本信息，只查询状态为启用的项目
        ProjectInfo projectInfo = projectInfoMapper.selectByProjectCode(projectCode);
        if (projectInfo == null) {
            return null;
        }

        // 2. 使用统一的构建方法构建完整信息
        return buildProjectInfoDto(projectInfo);
    }

    /**
     * 查询项目要素，优先查询临时配置，如果没有或已过期则查询长期配置
     *
     * @param projectCode 项目编码
     * @return 项目要素
     */
    private ProjectElements queryProjectElements(String projectCode) {
        LocalDateTime currentTime = LocalDateTime.now();

        // 1. 先查询有效的临时项目要素
        ProjectElements temporaryElements = projectElementsMapper.selectValidTemporaryElements(
                projectCode, AbleStatus.ENABLE, ProjectDurationType.TEMPORARY.name(), currentTime);

        if (temporaryElements != null) {
            return temporaryElements;
        }

        // 2. 没有有效的临时配置，查询长期配置
        ProjectElements longtimeElements = projectElementsMapper.selectByProjectCodeAndEnabledAndProjectDurationType(
                projectCode, AbleStatus.ENABLE, ProjectDurationType.LONGTIME.name());

        return longtimeElements;
    }

    /**
     * 计算缓存时长，如果是临时配置则使用临时配置的结束时间，否则使用默认时长
     *
     * @param elementsDto 项目要素dto
     * @return 缓存时长
     */
    private Duration calculateCacheDuration(ProjectElementsDto elementsDto) {
        if (elementsDto == null) {
            return CACHE_DURATION;
        }

        // 如果是临时配置且有结束时间，计算到结束时间的时长
        if (ProjectDurationType.TEMPORARY.equals(elementsDto.getProjectDurationType())
                && elementsDto.getTempEndTime() != null) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime endTime = elementsDto.getTempEndTime();

            if (endTime.isAfter(now)) {
                return Duration.between(now, endTime);
            }
        }

        // 长期配置或临时配置已过期，使用默认缓存时长
        return CACHE_DURATION;
    }

    /**
     * 查询所有生效项目信息
     *
     * @return 所有生效项目信息列表
     */
    @Override
    public List<ProjectInfoDto> queryAllEnabledProjects() {
        try {
            // 1. 查询所有启用状态的项目基本信息
            List<ProjectInfo> projectInfoList = projectInfoMapper.selectAllEnabledProjects();

            if (projectInfoList == null || projectInfoList.isEmpty()) {
                return new ArrayList<>();
            }

            // 2. 转换为DTO并填充完整信息
            List<ProjectInfoDto> projectInfoDtoList = new ArrayList<>();
            for (ProjectInfo projectInfo : projectInfoList) {
                ProjectInfoDto projectInfoDto = buildProjectInfoDto(projectInfo);
                if (projectInfoDto != null) {
                    projectInfoDtoList.add(projectInfoDto);
                }
            }

            return projectInfoDtoList;
        } catch (Exception e) {
            logger.error("查询所有生效项目信息异常", e);
            throw new RuntimeException("查询所有生效项目信息失败", e);
        }
    }

    /**
     * 构建项目完整信息DTO
     *
     * @param projectInfo 项目基本信息
     * @return 项目完整信息DTO
     */
    private ProjectInfoDto buildProjectInfoDto(ProjectInfo projectInfo) {
        try {
            // 1. 转换基本信息为DTO
            ProjectInfoDto projectInfoDto = new ProjectInfoDto();
            BeanUtil.copyProperties(projectInfo, projectInfoDto);
            // 去除项目信息中字符串字段首尾的空格
            StringUtils.trimAllStringFields(projectInfoDto);

            // 2. 查询项目要素
            ProjectElements elements = queryProjectElements(projectInfo.getProjectCode());
            if (ObjectUtil.isNotEmpty(elements)) {
                ProjectElementsDto elementsDto = new ProjectElementsDto();
                BeanUtil.copyProperties(elements, elementsDto);
                // 去除项目要素中字符串字段首尾的空格
                StringUtils.trimAllStringFields(elementsDto);
                projectInfoDto.setElements(elementsDto);
            } else {
                projectInfoDto.setElements(null);
            }

            // 3. 查询项目要素扩展
            ProjectElementsExt elementsExt = null;
            if (elements != null) {
                elementsExt = projectElementsExtMapper.selectByParentId(elements.getId());
            }
            ProjectElementsExtDto elementsExtDto = new ProjectElementsExtDto();
            if (elementsExt != null) {
                BeanUtil.copyProperties(elementsExt, elementsExtDto);
                // 去除项目要素扩展中字符串字段首尾的空格
                StringUtils.trimAllStringFields(elementsExtDto);
                projectInfoDto.setElementsExt(elementsExtDto);
            }

            return projectInfoDto;
        } catch (Exception e) {
            logger.info("构建项目完整信息异常，projectCode: {}", projectInfo.getProjectCode(), e);
            return null;
        }
    }



    /**
     * 清除项目信息缓存
     *
     * @param projectCode 项目编码
     */
    @Override
    public void clearProjectInfoCache(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            logger.info("项目编码为空，无法清除缓存");
            return;
        }

        String cacheKey = PROJECT_INFO_CACHE_PREFIX + projectCode;
        try {
            redisUtils.del(cacheKey);
        } catch (Exception e) {
            logger.info("清除项目信息缓存异常，projectCode: {}", projectCode, e);
        }
    }


    @Override
    public PageResult<ProjectInfoVo> queryAllPage(ProjectInfoQueryCriteria criteria){
        Page<ProjectInfoVo> page = new Page<>(criteria.getPage(),criteria.getSize());
        Page<ProjectInfoVo> pageList = projectInfoMapper.queryProjectInfoPage(page, criteria);
        return PageUtil.toPage(pageList.getRecords(),pageList.getTotal());
    }

    @Override
    public List<ProjectInfo> queryAll(ProjectInfoQueryCriteria criteria){
        //return projectInfoMapper.selectList(criteria);
        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProjectInfoDto resources) {
        this.checkProjectCode(resources.getProjectCode());
        ProjectInfo info = new ProjectInfo();
        BeanUtil.copyProperties(resources, info);

        String id = "P" + System.currentTimeMillis() + String.valueOf(Math.random()).substring(2,5) ;
        info.setId(id);
        String userId = String.valueOf(SecurityUtils.getCurrentUserId());
        LocalDateTime dateTime = LocalDateTime.now();
        info.setCreatedBy(userId);
        info.setCreatedTime(dateTime);
        projectInfoMapper.insert(info);

        // 清除项目信息缓存
        clearProjectInfoCache(resources.getProjectCode());

        if(ObjectUtil.isNotEmpty(resources.getElements())){
            ProjectElements elements = new ProjectElements();
            BeanUtil.copyProperties(resources.getElements(), elements);

            String elemId = "ELEM" + System.currentTimeMillis() + String.valueOf(Math.random()).substring(2,5) ;
            elements.setId(elemId);
            elements.setProjectCode(info.getProjectCode());
            elements.setCreatedBy(userId);
            elements.setCreatedTime(dateTime);
            projectElementsMapper.insert(elements);

            if(ObjectUtil.isNotEmpty(resources.getElementsExt())){
                ProjectElementsExt elementsExt = new ProjectElementsExt();
                BeanUtil.copyProperties(resources.getElementsExt(), elementsExt);

                String extId = "EXT" + System.currentTimeMillis() + String.valueOf(Math.random()).substring(2,5) ;
                elementsExt.setId(extId);
                elementsExt.setParentId(elemId);
                elementsExt.setProjectCode(info.getProjectCode());
                elementsExt.setCreatedBy(userId);
                elementsExt.setCreatedTime(dateTime);
                projectElementsExtMapper.insert(elementsExt);
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectInfoDto resources) {
        String userId = String.valueOf(SecurityUtils.getCurrentUserId());
        LocalDateTime dateTime = LocalDateTime.now();
        ProjectInfo projectInfo = projectInfoMapper.selectById(resources.getId());
        BeanUtil.copyProperties(resources, projectInfo);
        projectInfo.setUpdatedBy(userId);
        projectInfo.setUpdatedTime(dateTime);
        projectInfoMapper.updateById(projectInfo);

        if(ObjectUtil.isNotEmpty(resources.getElements())){
            ProjectElements elements = this.getProjectElements(resources.getProjectCode());
            BeanUtil.copyProperties(resources.getElements(), elements);
            elements.setUpdatedBy(userId);
            elements.setUpdatedTime(dateTime);
            projectElementsMapper.updateById(elements);

            if(ObjectUtil.isNotEmpty(resources.getElementsExt())){
                ProjectElementsExt projectElementExt = this.getProjectElementExt(elements.getId());
                BeanUtil.copyProperties(resources.getElementsExt(), projectElementExt);
                projectElementExt.setUpdatedBy(userId);
                projectElementExt.setUpdatedTime(dateTime);
                projectElementsExtMapper.updateById(projectElementExt);
            }
        }

        // 清除项目信息缓存
        clearProjectInfoCache(resources.getProjectCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<String> ids) {
        // 先获取要删除的项目信息，用于清除缓存
        List<ProjectInfo> projects = this.getProjects(ids);
        Set<String> projectCodes = projects.stream().map(ProjectInfo::getProjectCode).collect(Collectors.toSet());

        projectInfoMapper.deleteBatchIds(ids);
        List<ProjectElements> projectElements = getProjectElements(projectCodes);
        Set<String> collect1 = projectElements.stream().map(ProjectElements::getId).collect(Collectors.toSet());
        projectElementsMapper.deleteBatchIds(collect1);
        this.delProjectElementExt(collect1);

        // 清除相关项目的缓存
        for (String projectCode : projectCodes) {
            clearProjectInfoCache(projectCode);
        }
    }

    @Override
    public ProjectInfoVo getProjectInfo(String id) {
        ProjectInfoVo vo = new ProjectInfoVo();
        ProjectInfo info = projectInfoMapper.selectById(id);
        BeanUtil.copyProperties(info, vo);

        ProjectElementsVo elementsVo = new ProjectElementsVo();
        ProjectElements projectElements = getProjectElements(info.getProjectCode());
        BeanUtil.copyProperties(projectElements, elementsVo);
        vo.setElements(elementsVo);

        ProjectElementsExtVo elementsExtVo = new ProjectElementsExtVo();
        ProjectElementsExt projectElementExt = getProjectElementExt(projectElements.getId());
        BeanUtil.copyProperties(projectElementExt, elementsExtVo);
        vo.setElementsExt(elementsExtVo);

        return vo;
    }

    @Override
    public void download(List<ProjectInfo> all, HttpServletResponse response) throws IOException {
    }


    private void delProjectElementExt(Set<String> collect){
        LambdaQueryWrapper<ProjectElementsExt> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProjectElementsExt::getParentId,collect);
        projectElementsExtMapper.delete(lambdaQueryWrapper);
    }

    private List<ProjectElements> getProjectElements(Set<String> collect){
        LambdaQueryWrapper<ProjectElements> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProjectElements::getProjectCode,collect);
        return projectElementsMapper.selectList(lambdaQueryWrapper);
    }

    private List<ProjectInfo> getProjects(List<String> ids){
        LambdaQueryWrapper<ProjectInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProjectInfo::getId,ids);
        return projectInfoMapper.selectList(lambdaQueryWrapper);
    }

    private void checkProjectCode(String projectCode){
        LambdaQueryWrapper<ProjectInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectInfo::getProjectCode,projectCode);
        List<ProjectInfo> projectInfos = projectInfoMapper.selectList(lambdaQueryWrapper);
        if(ObjectUtil.isNotEmpty(projectInfos)){
            throw new RuntimeException("项目编码已重复");
        }
    }

    private ProjectElements getProjectElements(String projectCode){
        LambdaQueryWrapper<ProjectElements> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectElements::getProjectCode,projectCode);
        ProjectElements elements = projectElementsMapper.selectOne(lambdaQueryWrapper);
        if(ObjectUtil.isNotEmpty(elements)){
            return elements;
        }
        return null;
    }


    private ProjectElementsExt getProjectElementExt(String id){
        LambdaQueryWrapper<ProjectElementsExt> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectElementsExt::getParentId,id);
        ProjectElementsExt elementsExt = projectElementsExtMapper.selectOne(lambdaQueryWrapper);
        if(ObjectUtil.isNotEmpty(elementsExt)){
            return elementsExt;
        }
        return null;
    }

}
